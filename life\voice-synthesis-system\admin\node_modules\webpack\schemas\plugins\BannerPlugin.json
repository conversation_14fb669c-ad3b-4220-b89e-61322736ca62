{"definitions": {"BannerFunction": {"description": "The banner as function, it will be wrapped in a comment", "instanceof": "Function", "tsType": "(data: { hash: string, chunk: import('../../lib/Chunk'), filename: string, basename: string, query: string}) => string"}, "Rule": {"oneOf": [{"instanceof": "RegExp", "tsType": "RegExp"}, {"type": "string", "minLength": 1}]}, "Rules": {"oneOf": [{"type": "array", "items": {"description": "A rule condition", "anyOf": [{"$ref": "#/definitions/Rule"}]}}, {"$ref": "#/definitions/Rule"}]}}, "title": "BannerPluginArgument", "oneOf": [{"title": "BannerPluginOptions", "type": "object", "additionalProperties": false, "properties": {"banner": {"description": "Specifies the banner", "anyOf": [{"$ref": "#/definitions/BannerFunction"}, {"type": "string"}]}, "entryOnly": {"description": "If true, the banner will only be added to the entry chunks", "type": "boolean"}, "exclude": {"description": "Exclude all modules matching any of these conditions", "anyOf": [{"$ref": "#/definitions/Rules"}]}, "include": {"description": "Include all modules matching any of these conditions", "anyOf": [{"$ref": "#/definitions/Rules"}]}, "raw": {"description": "If true, banner will not be wrapped in a comment", "type": "boolean"}, "test": {"description": "Include all modules that pass test assertion", "anyOf": [{"$ref": "#/definitions/Rules"}]}}, "required": ["banner"]}, {"$ref": "#/definitions/BannerFunction"}, {"description": "The banner as string, it will be wrapped in a comment", "type": "string", "minLength": 1}]}