#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件格式转换器启动脚本
检查依赖并启动GUI应用程序
"""

import sys
import subprocess
import importlib.util

def check_dependencies():
    """检查必需的依赖包"""
    required_packages = {
        'PIL': 'Pillow',
        'pandas': 'pandas',
        'openpyxl': 'openpyxl',
        'docx': 'python-docx',
        'markdown': 'markdown',
        'docx2txt': 'docx2txt'
    }
    
    missing_packages = []
    
    for module_name, package_name in required_packages.items():
        if importlib.util.find_spec(module_name) is None:
            missing_packages.append(package_name)
    
    return missing_packages

def install_dependencies(packages):
    """安装缺失的依赖包"""
    print("正在安装缺失的依赖包...")
    for package in packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✓ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"✗ {package} 安装失败")
            return False
    return True

def main():
    """主函数"""
    print("文件格式转换器")
    print("=" * 30)
    
    # 检查依赖
    missing = check_dependencies()
    
    if missing:
        print(f"检测到缺失的依赖包: {', '.join(missing)}")
        response = input("是否自动安装? (y/n): ").lower().strip()
        
        if response in ['y', 'yes', '是']:
            if not install_dependencies(missing):
                print("依赖安装失败，请手动安装:")
                print("pip install -r requirements.txt")
                return
        else:
            print("请手动安装依赖:")
            print("pip install -r requirements.txt")
            return
    
    # 启动GUI应用程序
    try:
        print("启动文件转换器...")
        from file_converter_gui import main as gui_main
        gui_main()
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保 file_converter_gui.py 文件存在")
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == "__main__":
    main()
