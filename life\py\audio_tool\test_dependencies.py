#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试依赖是否正确安装
"""

def test_imports():
    """测试导入"""
    print("测试依赖导入...")
    
    try:
        import PySide6
        print(f"✓ PySide6 版本: {PySide6.__version__}")
    except ImportError as e:
        print(f"✗ PySide6 导入失败: {e}")
        return False
    
    try:
        import pydub
        print(f"✓ pydub 版本: {pydub.__version__}")
    except ImportError as e:
        print(f"✗ pydub 导入失败: {e}")
        return False
    
    try:
        import numpy
        print(f"✓ numpy 版本: {numpy.__version__}")
    except ImportError as e:
        print(f"✗ numpy 导入失败: {e}")
        return False
    
    return True

def test_audio_processing():
    """测试音频处理功能"""
    print("\n测试音频处理功能...")
    
    try:
        from pydub import AudioSegment
        from pydub.silence import split_on_silence, detect_nonsilent
        print("✓ 音频处理模块导入成功")
        
        # 创建一个简单的测试音频
        test_audio = AudioSegment.silent(duration=1000)  # 1秒静音
        print("✓ 音频对象创建成功")
        
        return True
    except Exception as e:
        print(f"✗ 音频处理测试失败: {e}")
        return False

def test_gui_components():
    """测试GUI组件"""
    print("\n测试GUI组件...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        print("✓ Qt组件导入成功")
        
        # 测试应用程序创建（不显示界面）
        import sys
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        print("✓ QApplication创建成功")
        
        return True
    except Exception as e:
        print(f"✗ GUI组件测试失败: {e}")
        return False

def main():
    """主函数"""
    print("音频处理工具依赖测试")
    print("=" * 40)
    
    success = True
    
    # 测试导入
    if not test_imports():
        success = False
    
    # 测试音频处理
    if not test_audio_processing():
        success = False
    
    # 测试GUI组件
    if not test_gui_components():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("✓ 所有测试通过！音频工具可以正常使用。")
        print("\n使用方法:")
        print("python audio_tool.py")
    else:
        print("✗ 部分测试失败，请检查依赖安装。")
        print("\n安装命令:")
        print("pip install -r requirements.txt")

if __name__ == "__main__":
    main()
