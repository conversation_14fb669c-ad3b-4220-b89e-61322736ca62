#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件转换器测试脚本
创建测试文件并验证转换功能
"""

import os
import json
import csv
from pathlib import Path
from PIL import Image
import pandas as pd

def create_test_files():
    """创建测试文件"""
    test_dir = Path("test_files")
    test_dir.mkdir(exist_ok=True)
    
    print("创建测试文件...")
    
    # 创建测试图片
    img = Image.new('RGB', (100, 100), color='red')
    img.save(test_dir / "test_image.png")
    print("✓ 创建测试图片: test_image.png")
    
    # 创建测试文本文件
    with open(test_dir / "test_text.txt", 'w', encoding='utf-8') as f:
        f.write("这是一个测试文本文件\n包含中文内容\n用于测试格式转换功能")
    print("✓ 创建测试文本: test_text.txt")
    
    # 创建测试Markdown文件
    with open(test_dir / "test_markdown.md", 'w', encoding='utf-8') as f:
        f.write("# 测试标题\n\n这是一个**测试**Markdown文件\n\n- 列表项1\n- 列表项2")
    print("✓ 创建测试Markdown: test_markdown.md")
    
    # 创建测试CSV文件
    with open(test_dir / "test_data.csv", 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['姓名', '年龄', '城市'])
        writer.writerow(['张三', '25', '北京'])
        writer.writerow(['李四', '30', '上海'])
        writer.writerow(['王五', '28', '广州'])
    print("✓ 创建测试CSV: test_data.csv")
    
    # 创建测试JSON文件
    test_data = [
        {"name": "张三", "age": 25, "city": "北京"},
        {"name": "李四", "age": 30, "city": "上海"},
        {"name": "王五", "age": 28, "city": "广州"}
    ]
    with open(test_dir / "test_data.json", 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    print("✓ 创建测试JSON: test_data.json")

    # 创建测试DOCX文件（用于测试DOC转换）
    try:
        from docx import Document
        doc = Document()
        doc.add_heading('测试文档', 0)
        doc.add_paragraph('这是一个测试DOCX文档，用于测试DOC格式转换功能。')
        doc.add_paragraph('包含多个段落和中文内容。')
        doc.add_heading('子标题', level=1)
        doc.add_paragraph('这是子标题下的内容。')
        doc.save(test_dir / "test_document.docx")
        print("✓ 创建测试DOCX: test_document.docx")
    except ImportError:
        print("⚠ 跳过DOCX文件创建（缺少python-docx）")

    print(f"\n测试文件已创建在 {test_dir.absolute()} 文件夹中")
    return test_dir

def test_converter():
    """测试转换器功能"""
    from file_converter_gui import FileConverter
    
    converter = FileConverter()
    test_dir = create_test_files()
    output_dir = Path("test_output")
    output_dir.mkdir(exist_ok=True)
    
    print("\n开始测试转换功能...")
    
    # 测试图片转换
    try:
        source_file = test_dir / "test_image.png"
        target_file = output_dir / "test_image.jpg"
        success = converter.convert_file(str(source_file), str(target_file), '.png', '.jpg')
        print(f"{'✓' if success else '✗'} 图片转换 PNG -> JPG: {success}")
    except Exception as e:
        print(f"✗ 图片转换失败: {e}")
    
    # 测试文档转换
    try:
        source_file = test_dir / "test_markdown.md"
        target_file = output_dir / "test_markdown.html"
        success = converter.convert_file(str(source_file), str(target_file), '.md', '.html')
        print(f"{'✓' if success else '✗'} 文档转换 MD -> HTML: {success}")
    except Exception as e:
        print(f"✗ 文档转换失败: {e}")
    
    # 测试数据转换
    try:
        source_file = test_dir / "test_data.csv"
        target_file = output_dir / "test_data.json"
        success = converter.convert_file(str(source_file), str(target_file), '.csv', '.json')
        print(f"{'✓' if success else '✗'} 数据转换 CSV -> JSON: {success}")
    except Exception as e:
        print(f"✗ 数据转换失败: {e}")
    
    try:
        source_file = test_dir / "test_data.json"
        target_file = output_dir / "test_data_from_json.csv"
        success = converter.convert_file(str(source_file), str(target_file), '.json', '.csv')
        print(f"{'✓' if success else '✗'} 数据转换 JSON -> CSV: {success}")
    except Exception as e:
        print(f"✗ 数据转换失败: {e}")

    # 测试DOCX转DOC转换（如果DOCX文件存在）
    docx_file = test_dir / "test_document.docx"
    if docx_file.exists():
        try:
            target_file = output_dir / "test_document.doc"
            success = converter.convert_file(str(docx_file), str(target_file), '.docx', '.doc')
            print(f"{'✓' if success else '✗'} 文档转换 DOCX -> DOC: {success}")
        except Exception as e:
            print(f"✗ DOCX到DOC转换失败: {e}")

    print(f"\n转换结果已保存在 {output_dir.absolute()} 文件夹中")
    
    # 显示支持的格式
    print("\n支持的转换格式:")
    for category, info in converter.supported_conversions.items():
        print(f"{category.upper()}:")
        print(f"  输入格式: {', '.join(info['extensions'])}")
        print(f"  输出格式: {', '.join(info['target_formats'])}")

def main():
    """主函数"""
    print("文件转换器测试程序")
    print("=" * 40)
    
    try:
        test_converter()
        print("\n测试完成!")
        print("\n要启动GUI界面，请运行:")
        print("python file_converter_gui.py")
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装所需依赖:")
        print("pip install -r requirements.txt")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
