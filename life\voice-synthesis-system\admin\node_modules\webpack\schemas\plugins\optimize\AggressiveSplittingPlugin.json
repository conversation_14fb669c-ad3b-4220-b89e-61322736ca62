{"title": "AggressiveSplittingPluginOptions", "type": "object", "additionalProperties": false, "properties": {"chunkOverhead": {"description": "Default: 0", "type": "number"}, "entryChunkMultiplicator": {"description": "Default: 1", "type": "number"}, "maxSize": {"description": "Byte, maxsize of per file. Default: 51200", "type": "number"}, "minSize": {"description": "Byte, split point. Default: 30720", "type": "number"}}}