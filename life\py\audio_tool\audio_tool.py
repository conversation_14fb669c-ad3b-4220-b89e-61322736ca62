#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频处理工具
功能：
1. 音频切割（均匀切割和基于停顿的智能切割）
2. 音频合并（支持自定义停顿时间）
3. 音频停顿处理（去除停顿或自定义停顿）
"""

import sys
import os
import threading
from pathlib import Path
from typing import List, Optional

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout, 
    QHBoxLayout, QLabel, QPushButton, QFileDialog, QSpinBox, 
    QDoubleSpinBox, QComboBox, QTextEdit, QProgressBar, QGroupBox,
    QRadioButton, QButtonGroup, QListWidget, QMessageBox, QCheckBox
)
from PySide6.QtCore import Qt, QThread, QObject, Signal
from PySide6.QtGui import QFont

try:
    from pydub import AudioSegment
    from pydub.silence import split_on_silence, detect_nonsilent
    import numpy as np
except ImportError as e:
    print(f"缺少依赖包: {e}")
    print("请安装: pip install pydub numpy")
    sys.exit(1)


class AudioProcessor(QObject):
    """音频处理后端类"""
    
    progress_updated = Signal(int)
    status_updated = Signal(str)
    finished = Signal(str)
    error_occurred = Signal(str)
    
    def __init__(self):
        super().__init__()
        
    def split_audio_evenly(self, audio_path: str, segment_duration: int, output_dir: str):
        """均匀切割音频"""
        try:
            self.status_updated.emit("正在加载音频文件...")
            audio = AudioSegment.from_file(audio_path)
            
            segment_length_ms = segment_duration * 1000
            total_segments = len(audio) // segment_length_ms + (1 if len(audio) % segment_length_ms else 0)
            
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            base_name = Path(audio_path).stem
            
            for i in range(total_segments):
                start_time = i * segment_length_ms
                end_time = min((i + 1) * segment_length_ms, len(audio))
                
                segment = audio[start_time:end_time]
                output_file = output_path / f"{base_name}_part_{i+1:03d}.wav"
                
                self.status_updated.emit(f"正在保存片段 {i+1}/{total_segments}...")
                segment.export(str(output_file), format="wav")
                
                progress = int((i + 1) / total_segments * 100)
                self.progress_updated.emit(progress)
            
            self.finished.emit(f"成功切割为 {total_segments} 个片段")
            
        except Exception as e:
            self.error_occurred.emit(f"切割失败: {str(e)}")
    
    def split_audio_by_silence(self, audio_path: str, min_duration: int, max_duration: int, 
                              silence_thresh: int, output_dir: str):
        """基于停顿智能切割音频"""
        try:
            self.status_updated.emit("正在加载音频文件...")
            audio = AudioSegment.from_file(audio_path)
            
            # 检测非静音片段
            nonsilent_ranges = detect_nonsilent(
                audio, 
                min_silence_len=500,  # 最小静音长度500ms
                silence_thresh=silence_thresh
            )
            
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            base_name = Path(audio_path).stem
            segments = []
            current_segment_start = 0
            
            min_duration_ms = min_duration * 1000
            max_duration_ms = max_duration * 1000
            
            for i, (start, end) in enumerate(nonsilent_ranges):
                current_duration = end - current_segment_start
                
                # 如果当前段落超过最大时长，或者是最后一个片段
                if current_duration >= max_duration_ms or i == len(nonsilent_ranges) - 1:
                    if current_duration >= min_duration_ms:
                        segments.append((current_segment_start, end))
                        current_segment_start = end
                    elif segments:  # 如果太短，合并到上一个片段
                        last_start, _ = segments[-1]
                        segments[-1] = (last_start, end)
                        current_segment_start = end
            
            # 导出片段
            for i, (start, end) in enumerate(segments):
                segment = audio[start:end]
                output_file = output_path / f"{base_name}_segment_{i+1:03d}.wav"
                
                self.status_updated.emit(f"正在保存片段 {i+1}/{len(segments)}...")
                segment.export(str(output_file), format="wav")
                
                progress = int((i + 1) / len(segments) * 100)
                self.progress_updated.emit(progress)
            
            self.finished.emit(f"成功切割为 {len(segments)} 个片段")
            
        except Exception as e:
            self.error_occurred.emit(f"智能切割失败: {str(e)}")
    
    def merge_audio_files(self, audio_files: List[str], pause_duration: float, output_file: str):
        """合并音频文件"""
        try:
            if not audio_files:
                self.error_occurred.emit("没有选择音频文件")
                return
            
            self.status_updated.emit("正在加载音频文件...")
            combined = AudioSegment.empty()
            pause = AudioSegment.silent(duration=int(pause_duration * 1000))
            
            for i, audio_file in enumerate(audio_files):
                self.status_updated.emit(f"正在处理文件 {i+1}/{len(audio_files)}...")
                audio = AudioSegment.from_file(audio_file)
                
                if i > 0:  # 除了第一个文件，其他文件前都加停顿
                    combined += pause
                
                combined += audio
                
                progress = int((i + 1) / len(audio_files) * 100)
                self.progress_updated.emit(progress)
            
            self.status_updated.emit("正在保存合并后的音频...")
            combined.export(output_file, format="wav")
            
            self.finished.emit(f"成功合并 {len(audio_files)} 个音频文件")
            
        except Exception as e:
            self.error_occurred.emit(f"合并失败: {str(e)}")
    
    def process_silence(self, audio_path: str, output_file: str, operation: str, 
                       silence_thresh: int, custom_pause: float = 0):
        """处理音频停顿"""
        try:
            self.status_updated.emit("正在加载音频文件...")
            audio = AudioSegment.from_file(audio_path)
            
            if operation == "remove":
                # 去除停顿
                self.status_updated.emit("正在去除停顿...")
                chunks = split_on_silence(
                    audio,
                    min_silence_len=500,
                    silence_thresh=silence_thresh,
                    keep_silence=100  # 保留100ms的静音
                )
                
                if chunks:
                    result = chunks[0]
                    for chunk in chunks[1:]:
                        result += chunk
                else:
                    result = audio
                    
            elif operation == "custom":
                # 自定义停顿
                self.status_updated.emit("正在设置自定义停顿...")
                chunks = split_on_silence(
                    audio,
                    min_silence_len=500,
                    silence_thresh=silence_thresh,
                    keep_silence=0
                )
                
                if chunks:
                    custom_silence = AudioSegment.silent(duration=int(custom_pause * 1000))
                    result = chunks[0]
                    for chunk in chunks[1:]:
                        result += custom_silence + chunk
                else:
                    result = audio
            
            self.status_updated.emit("正在保存处理后的音频...")
            result.export(output_file, format="wav")
            
            self.finished.emit("音频停顿处理完成")
            
        except Exception as e:
            self.error_occurred.emit(f"停顿处理失败: {str(e)}")


class WorkerThread(QThread):
    """工作线程"""
    
    def __init__(self, processor: AudioProcessor, func_name: str, *args, **kwargs):
        super().__init__()
        self.processor = processor
        self.func_name = func_name
        self.args = args
        self.kwargs = kwargs
    
    def run(self):
        func = getattr(self.processor, self.func_name)
        func(*self.args, **self.kwargs)


class AudioSplitterTab(QWidget):
    """音频切割选项卡"""
    
    def __init__(self):
        super().__init__()
        self.audio_file = ""
        self.output_dir = ""
        self.processor = AudioProcessor()
        self.worker_thread = None
        self.init_ui()
        self.connect_signals()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 文件选择
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout()
        
        file_select_layout = QHBoxLayout()
        self.file_label = QLabel("未选择文件")
        self.file_btn = QPushButton("选择音频文件")
        file_select_layout.addWidget(self.file_label)
        file_select_layout.addWidget(self.file_btn)
        file_layout.addLayout(file_select_layout)
        
        output_select_layout = QHBoxLayout()
        self.output_label = QLabel("未选择输出目录")
        self.output_btn = QPushButton("选择输出目录")
        output_select_layout.addWidget(self.output_label)
        output_select_layout.addWidget(self.output_btn)
        file_layout.addLayout(output_select_layout)
        
        file_group.setLayout(file_layout)
        layout.addWidget(file_group)
        
        # 切割模式选择
        mode_group = QGroupBox("切割模式")
        mode_layout = QVBoxLayout()
        
        self.mode_group = QButtonGroup()
        self.even_radio = QRadioButton("均匀切割")
        self.smart_radio = QRadioButton("智能切割（基于停顿）")
        self.even_radio.setChecked(True)
        
        self.mode_group.addButton(self.even_radio, 0)
        self.mode_group.addButton(self.smart_radio, 1)
        
        mode_layout.addWidget(self.even_radio)
        mode_layout.addWidget(self.smart_radio)
        mode_group.setLayout(mode_layout)
        layout.addWidget(mode_group)
        
        # 参数设置
        params_group = QGroupBox("参数设置")
        params_layout = QVBoxLayout()

        # 均匀切割参数
        even_layout = QHBoxLayout()
        even_layout.addWidget(QLabel("切割时长(秒):"))
        self.duration_spin = QSpinBox()
        self.duration_spin.setRange(1, 3600)
        self.duration_spin.setValue(30)
        even_layout.addWidget(self.duration_spin)
        even_layout.addStretch()
        params_layout.addLayout(even_layout)

        # 智能切割参数
        smart_layout = QVBoxLayout()

        time_range_layout = QHBoxLayout()
        time_range_layout.addWidget(QLabel("时间范围:"))
        time_range_layout.addWidget(QLabel("最小"))
        self.min_duration_spin = QSpinBox()
        self.min_duration_spin.setRange(5, 300)
        self.min_duration_spin.setValue(10)
        time_range_layout.addWidget(self.min_duration_spin)
        time_range_layout.addWidget(QLabel("秒"))

        time_range_layout.addWidget(QLabel("最大"))
        self.max_duration_spin = QSpinBox()
        self.max_duration_spin.setRange(10, 600)
        self.max_duration_spin.setValue(60)
        time_range_layout.addWidget(self.max_duration_spin)
        time_range_layout.addWidget(QLabel("秒"))
        time_range_layout.addStretch()
        smart_layout.addLayout(time_range_layout)

        silence_layout = QHBoxLayout()
        silence_layout.addWidget(QLabel("静音阈值(dB):"))
        self.silence_thresh_spin = QSpinBox()
        self.silence_thresh_spin.setRange(-80, -10)
        self.silence_thresh_spin.setValue(-40)
        silence_layout.addWidget(self.silence_thresh_spin)
        silence_layout.addStretch()
        smart_layout.addLayout(silence_layout)

        params_layout.addLayout(smart_layout)
        params_group.setLayout(params_layout)
        layout.addWidget(params_group)

        # 控制按钮
        control_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始切割")
        self.start_btn.setEnabled(False)
        control_layout.addWidget(self.start_btn)
        control_layout.addStretch()
        layout.addLayout(control_layout)

        # 进度和状态
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)

        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(100)
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)

        self.setLayout(layout)

    def connect_signals(self):
        self.file_btn.clicked.connect(self.select_audio_file)
        self.output_btn.clicked.connect(self.select_output_dir)
        self.start_btn.clicked.connect(self.start_splitting)

        self.processor.progress_updated.connect(self.progress_bar.setValue)
        self.processor.status_updated.connect(self.append_status)
        self.processor.finished.connect(self.on_finished)
        self.processor.error_occurred.connect(self.on_error)

    def select_audio_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择音频文件", "",
            "音频文件 (*.mp3 *.wav *.m4a *.flac *.ogg);;所有文件 (*)"
        )
        if file_path:
            self.audio_file = file_path
            self.file_label.setText(f"已选择: {Path(file_path).name}")
            self.check_ready()

    def select_output_dir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir = dir_path
            self.output_label.setText(f"输出到: {dir_path}")
            self.check_ready()

    def check_ready(self):
        self.start_btn.setEnabled(bool(self.audio_file and self.output_dir))

    def start_splitting(self):
        if not self.audio_file or not self.output_dir:
            return

        self.start_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        self.status_text.clear()

        if self.even_radio.isChecked():
            # 均匀切割
            duration = self.duration_spin.value()
            self.worker_thread = WorkerThread(
                self.processor, 'split_audio_evenly',
                self.audio_file, duration, self.output_dir
            )
        else:
            # 智能切割
            min_dur = self.min_duration_spin.value()
            max_dur = self.max_duration_spin.value()
            silence_thresh = self.silence_thresh_spin.value()
            self.worker_thread = WorkerThread(
                self.processor, 'split_audio_by_silence',
                self.audio_file, min_dur, max_dur, silence_thresh, self.output_dir
            )

        self.worker_thread.finished.connect(self.on_thread_finished)
        self.worker_thread.start()

    def append_status(self, message):
        self.status_text.append(message)

    def on_finished(self, message):
        self.append_status(message)
        QMessageBox.information(self, "完成", message)

    def on_error(self, message):
        self.append_status(f"错误: {message}")
        QMessageBox.critical(self, "错误", message)

    def on_thread_finished(self):
        self.start_btn.setEnabled(True)


class AudioMergerTab(QWidget):
    """音频合并选项卡"""

    def __init__(self):
        super().__init__()
        self.audio_files = []
        self.output_file = ""
        self.processor = AudioProcessor()
        self.worker_thread = None
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        layout = QVBoxLayout()

        # 文件选择
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout()

        file_btn_layout = QHBoxLayout()
        self.add_files_btn = QPushButton("添加音频文件")
        self.clear_files_btn = QPushButton("清空列表")
        file_btn_layout.addWidget(self.add_files_btn)
        file_btn_layout.addWidget(self.clear_files_btn)
        file_btn_layout.addStretch()
        file_layout.addLayout(file_btn_layout)

        self.files_list = QListWidget()
        self.files_list.setMaximumHeight(150)
        file_layout.addWidget(self.files_list)

        output_layout = QHBoxLayout()
        self.output_label = QLabel("未选择输出文件")
        self.output_btn = QPushButton("选择输出文件")
        output_layout.addWidget(self.output_label)
        output_layout.addWidget(self.output_btn)
        file_layout.addLayout(output_layout)

        file_group.setLayout(file_layout)
        layout.addWidget(file_group)

        # 参数设置
        params_group = QGroupBox("参数设置")
        params_layout = QHBoxLayout()

        params_layout.addWidget(QLabel("音频间停顿时间(秒):"))
        self.pause_spin = QDoubleSpinBox()
        self.pause_spin.setRange(0, 10)
        self.pause_spin.setValue(1.0)
        self.pause_spin.setSingleStep(0.1)
        params_layout.addWidget(self.pause_spin)
        params_layout.addStretch()

        params_group.setLayout(params_layout)
        layout.addWidget(params_group)

        # 控制按钮
        control_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始合并")
        self.start_btn.setEnabled(False)
        control_layout.addWidget(self.start_btn)
        control_layout.addStretch()
        layout.addLayout(control_layout)

        # 进度和状态
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)

        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(100)
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)

        self.setLayout(layout)

    def connect_signals(self):
        self.add_files_btn.clicked.connect(self.add_audio_files)
        self.clear_files_btn.clicked.connect(self.clear_files)
        self.output_btn.clicked.connect(self.select_output_file)
        self.start_btn.clicked.connect(self.start_merging)

        self.processor.progress_updated.connect(self.progress_bar.setValue)
        self.processor.status_updated.connect(self.append_status)
        self.processor.finished.connect(self.on_finished)
        self.processor.error_occurred.connect(self.on_error)

    def add_audio_files(self):
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择音频文件", "",
            "音频文件 (*.mp3 *.wav *.m4a *.flac *.ogg);;所有文件 (*)"
        )
        if file_paths:
            for file_path in file_paths:
                if file_path not in self.audio_files:
                    self.audio_files.append(file_path)
                    self.files_list.addItem(Path(file_path).name)
            self.check_ready()

    def clear_files(self):
        self.audio_files.clear()
        self.files_list.clear()
        self.check_ready()

    def select_output_file(self):
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存合并后的音频", "",
            "WAV文件 (*.wav);;MP3文件 (*.mp3);;所有文件 (*)"
        )
        if file_path:
            self.output_file = file_path
            self.output_label.setText(f"保存到: {Path(file_path).name}")
            self.check_ready()

    def check_ready(self):
        self.start_btn.setEnabled(bool(self.audio_files and self.output_file))

    def start_merging(self):
        if not self.audio_files or not self.output_file:
            return

        self.start_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        self.status_text.clear()

        pause_duration = self.pause_spin.value()
        self.worker_thread = WorkerThread(
            self.processor, 'merge_audio_files',
            self.audio_files, pause_duration, self.output_file
        )

        self.worker_thread.finished.connect(self.on_thread_finished)
        self.worker_thread.start()

    def append_status(self, message):
        self.status_text.append(message)

    def on_finished(self, message):
        self.append_status(message)
        QMessageBox.information(self, "完成", message)

    def on_error(self, message):
        self.append_status(f"错误: {message}")
        QMessageBox.critical(self, "错误", message)

    def on_thread_finished(self):
        self.start_btn.setEnabled(True)


class AudioSilenceTab(QWidget):
    """音频停顿处理选项卡"""

    def __init__(self):
        super().__init__()
        self.audio_file = ""
        self.output_file = ""
        self.processor = AudioProcessor()
        self.worker_thread = None
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        layout = QVBoxLayout()

        # 文件选择
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout()

        input_layout = QHBoxLayout()
        self.input_label = QLabel("未选择输入文件")
        self.input_btn = QPushButton("选择音频文件")
        input_layout.addWidget(self.input_label)
        input_layout.addWidget(self.input_btn)
        file_layout.addLayout(input_layout)

        output_layout = QHBoxLayout()
        self.output_label = QLabel("未选择输出文件")
        self.output_btn = QPushButton("选择输出文件")
        output_layout.addWidget(self.output_label)
        output_layout.addWidget(self.output_btn)
        file_layout.addLayout(output_layout)

        file_group.setLayout(file_layout)
        layout.addWidget(file_group)

        # 操作模式选择
        mode_group = QGroupBox("操作模式")
        mode_layout = QVBoxLayout()

        self.mode_group = QButtonGroup()
        self.remove_radio = QRadioButton("去除停顿")
        self.custom_radio = QRadioButton("自定义停顿")
        self.remove_radio.setChecked(True)

        self.mode_group.addButton(self.remove_radio, 0)
        self.mode_group.addButton(self.custom_radio, 1)

        mode_layout.addWidget(self.remove_radio)
        mode_layout.addWidget(self.custom_radio)
        mode_group.setLayout(mode_layout)
        layout.addWidget(mode_group)

        # 参数设置
        params_group = QGroupBox("参数设置")
        params_layout = QVBoxLayout()

        silence_layout = QHBoxLayout()
        silence_layout.addWidget(QLabel("静音阈值(dB):"))
        self.silence_thresh_spin = QSpinBox()
        self.silence_thresh_spin.setRange(-80, -10)
        self.silence_thresh_spin.setValue(-40)
        silence_layout.addWidget(self.silence_thresh_spin)
        silence_layout.addStretch()
        params_layout.addLayout(silence_layout)

        custom_layout = QHBoxLayout()
        custom_layout.addWidget(QLabel("自定义停顿时间(秒):"))
        self.custom_pause_spin = QDoubleSpinBox()
        self.custom_pause_spin.setRange(0, 5)
        self.custom_pause_spin.setValue(0.5)
        self.custom_pause_spin.setSingleStep(0.1)
        custom_layout.addWidget(self.custom_pause_spin)
        custom_layout.addStretch()
        params_layout.addLayout(custom_layout)

        params_group.setLayout(params_layout)
        layout.addWidget(params_group)

        # 控制按钮
        control_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始处理")
        self.start_btn.setEnabled(False)
        control_layout.addWidget(self.start_btn)
        control_layout.addStretch()
        layout.addLayout(control_layout)

        # 进度和状态
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)

        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(100)
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)

        self.setLayout(layout)

    def connect_signals(self):
        self.input_btn.clicked.connect(self.select_input_file)
        self.output_btn.clicked.connect(self.select_output_file)
        self.start_btn.clicked.connect(self.start_processing)

        self.processor.progress_updated.connect(self.progress_bar.setValue)
        self.processor.status_updated.connect(self.append_status)
        self.processor.finished.connect(self.on_finished)
        self.processor.error_occurred.connect(self.on_error)

    def select_input_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择音频文件", "",
            "音频文件 (*.mp3 *.wav *.m4a *.flac *.ogg);;所有文件 (*)"
        )
        if file_path:
            self.audio_file = file_path
            self.input_label.setText(f"输入: {Path(file_path).name}")
            self.check_ready()

    def select_output_file(self):
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存处理后的音频", "",
            "WAV文件 (*.wav);;MP3文件 (*.mp3);;所有文件 (*)"
        )
        if file_path:
            self.output_file = file_path
            self.output_label.setText(f"输出: {Path(file_path).name}")
            self.check_ready()

    def check_ready(self):
        self.start_btn.setEnabled(bool(self.audio_file and self.output_file))

    def start_processing(self):
        if not self.audio_file or not self.output_file:
            return

        self.start_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        self.status_text.clear()

        operation = "remove" if self.remove_radio.isChecked() else "custom"
        silence_thresh = self.silence_thresh_spin.value()
        custom_pause = self.custom_pause_spin.value()

        self.worker_thread = WorkerThread(
            self.processor, 'process_silence',
            self.audio_file, self.output_file, operation,
            silence_thresh, custom_pause
        )

        self.worker_thread.finished.connect(self.on_thread_finished)
        self.worker_thread.start()

    def append_status(self, message):
        self.status_text.append(message)

    def on_finished(self, message):
        self.append_status(message)
        QMessageBox.information(self, "完成", message)

    def on_error(self, message):
        self.append_status(f"错误: {message}")
        QMessageBox.critical(self, "错误", message)

    def on_thread_finished(self):
        self.start_btn.setEnabled(True)


class AudioToolMainWindow(QMainWindow):
    """主窗口"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("音频处理工具")
        self.setGeometry(100, 100, 800, 600)

        # 创建选项卡
        tab_widget = QTabWidget()

        # 添加各个功能选项卡
        tab_widget.addTab(AudioSplitterTab(), "音频切割")
        tab_widget.addTab(AudioMergerTab(), "音频合并")
        tab_widget.addTab(AudioSilenceTab(), "停顿处理")

        self.setCentralWidget(tab_widget)

        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                text-align: center;
                font-size: 14px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QProgressBar {
                border: 2px solid #cccccc;
                border-radius: 5px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("音频处理工具")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("AudioTools")

    # 创建主窗口
    window = AudioToolMainWindow()
    window.show()

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
