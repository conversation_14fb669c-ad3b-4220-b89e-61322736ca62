# 语音合成系统管理后台

## 快速启动

### 推荐方法：使用简化配置
```bash
# 安装依赖
npm install

# 使用简化的开发服务器（推荐）
npm run dev-simple
```

### 备用方法：
```bash
# 方法1：使用安全启动脚本
npm run dev-safe

# 方法2：使用标准webpack配置
npm run dev

# 方法3：使用备用启动脚本
npm run dev-alt
```

## 访问地址
- 管理后台: http://localhost:8082

## 默认登录账号
- 用户名: admin
- 密码: admin123

## 主要功能
- 仪表板 - 系统概览和数据统计
- 用户管理 - 用户信息管理和状态控制
- 合成管理 - 语音合成记录查看和管理
- 积分管理 - 积分记录查看和统计
- 系统设置 - 系统参数配置和状态监控

## 故障排除

### 如果遇到webpack配置问题
1. 删除node_modules文件夹：`rm -rf node_modules`
2. 重新安装依赖：`npm install`
3. 尝试不同的启动方式：
   - `npm run dev-simple` （推荐）
   - `npm run dev-safe` （最安全）
   - `npm run dev-alt` （备用）

### 如果遇到端口占用
修改 `config/index.js` 中的端口配置，或者使用：
```bash
PORT=8083 npm run dev
```

## 项目结构
```
src/
├── layout/        # 布局组件
├── views/         # 页面组件
├── router/        # 路由配置
├── store/         # Vuex状态管理
└── main.js        # 入口文件
```
