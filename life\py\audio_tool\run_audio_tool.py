#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频处理工具启动脚本
"""

import sys
import subprocess
import os
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import PySide6
        import pydub
        import numpy
        print("✓ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        return False

def install_dependencies():
    """安装依赖"""
    print("正在安装依赖...")
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        print("✓ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 依赖安装失败: {e}")
        return False

def main():
    """主函数"""
    print("音频处理工具启动器")
    print("=" * 30)
    
    # 检查依赖
    if not check_dependencies():
        print("\n是否自动安装依赖? (y/n): ", end="")
        choice = input().lower().strip()
        
        if choice in ['y', 'yes', '是']:
            if not install_dependencies():
                print("依赖安装失败，请手动安装:")
                print(f"pip install -r {Path(__file__).parent / 'requirements.txt'}")
                return
        else:
            print("请手动安装依赖后再运行工具")
            return
    
    # 启动音频工具
    print("\n正在启动音频处理工具...")
    try:
        from audio_tool import main as audio_main
        audio_main()
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == "__main__":
    main()
