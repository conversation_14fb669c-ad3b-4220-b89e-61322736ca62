# 语音合成系统前端

## 快速启动

### 方法一：使用标准webpack配置
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 方法二：使用简化配置（推荐）
```bash
# 安装依赖
npm install

# 使用简化的开发服务器
npm run dev-simple
```

## 访问地址
- 开发服务器: http://localhost:8081

## 故障排除

### 如果遇到webpack配置问题
1. 删除node_modules文件夹
2. 重新安装依赖：`npm install`
3. 使用简化启动：`npm run dev-simple`

### 如果遇到端口占用
修改 `config/index.js` 中的端口配置，或者使用：
```bash
PORT=8082 npm run dev
```

## 项目结构
```
src/
├── components/     # 公共组件
├── views/         # 页面组件
├── router/        # 路由配置
├── store/         # Vuex状态管理
├── assets/        # 静态资源
└── main.js        # 入口文件
```

## 主要功能
- 用户登录/注册
- 语音合成
- 积分管理
- 合成记录查看
- 个人中心
