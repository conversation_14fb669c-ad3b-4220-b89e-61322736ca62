# 音频处理工具

一个基于PySide6的简洁音频处理工具，提供音频切割、合并和停顿处理功能。

## 功能特性

### 1. 音频切割
- **均匀切割**: 按指定时长将音频均匀分割
- **智能切割**: 基于音频停顿进行智能分割，支持时间范围控制

### 2. 音频合并
- 支持多个音频文件合并
- 可自定义音频间的停顿时间
- 支持多种音频格式

### 3. 停顿处理
- **去除停顿**: 自动检测并去除音频中的静音段
- **自定义停顿**: 将音频中的停顿替换为指定时长的静音

## 安装要求

### 系统要求
- Python 3.8+
- Windows/macOS/Linux

### 依赖包
```
PySide6>=6.5.0
pydub>=0.25.1
numpy>=1.21.0
```

### 音频处理依赖
pydub需要FFmpeg来处理各种音频格式：

**Windows:**
```bash
# 使用chocolatey安装
choco install ffmpeg

# 或下载预编译版本
# https://ffmpeg.org/download.html
```

**macOS:**
```bash
# 使用homebrew安装
brew install ffmpeg
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install ffmpeg
```

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行工具
```bash
# 方式1: 直接运行主程序
python audio_tool.py

# 方式2: 使用启动脚本（会自动检查依赖）
python run_audio_tool.py
```

## 使用说明

### 音频切割

#### 均匀切割
1. 选择要切割的音频文件
2. 选择输出目录
3. 选择"均匀切割"模式
4. 设置切割时长（秒）
5. 点击"开始切割"

#### 智能切割
1. 选择要切割的音频文件
2. 选择输出目录
3. 选择"智能切割（基于停顿）"模式
4. 设置时间范围：
   - 最小时长：每个片段的最小时长
   - 最大时长：每个片段的最大时长
5. 设置静音阈值（dB）：用于检测停顿的音量阈值
6. 点击"开始切割"

### 音频合并

1. 点击"添加音频文件"选择要合并的音频文件
2. 可以多次添加文件，或使用"清空列表"重新选择
3. 选择输出文件位置和名称
4. 设置音频间停顿时间（秒）
5. 点击"开始合并"

### 停顿处理

#### 去除停顿
1. 选择输入音频文件
2. 选择输出文件位置
3. 选择"去除停顿"模式
4. 设置静音阈值（dB）
5. 点击"开始处理"

#### 自定义停顿
1. 选择输入音频文件
2. 选择输出文件位置
3. 选择"自定义停顿"模式
4. 设置静音阈值（dB）
5. 设置自定义停顿时间（秒）
6. 点击"开始处理"

## 支持的音频格式

- **输入格式**: MP3, WAV, M4A, FLAC, OGG等
- **输出格式**: WAV, MP3

## 参数说明

### 静音阈值 (dB)
- 范围：-80 到 -10 dB
- 默认：-40 dB
- 说明：低于此阈值的音频被认为是静音
- 调整建议：
  - 背景噪音较大时，使用较高值（如-30 dB）
  - 录音质量较好时，使用较低值（如-50 dB）

### 时间范围设置
- **最小时长**：防止生成过短的音频片段
- **最大时长**：控制单个片段的最大长度
- 智能切割会在满足时间范围的前提下，在停顿点进行切割

## 注意事项

1. **文件路径**: 避免使用包含特殊字符的文件路径
2. **文件大小**: 处理大文件时可能需要较长时间，请耐心等待
3. **内存使用**: 处理超大音频文件时注意内存使用情况
4. **格式兼容**: 建议使用常见的音频格式以确保最佳兼容性

## 故障排除

### 常见问题

**Q: 提示缺少FFmpeg**
A: 请按照上述说明安装FFmpeg

**Q: 音频文件无法加载**
A: 检查文件格式是否支持，尝试转换为WAV格式

**Q: 切割结果不理想**
A: 调整静音阈值参数，或尝试不同的切割模式

**Q: 程序运行缓慢**
A: 大文件处理需要时间，可以先用小文件测试参数

### 错误日志
程序运行时的状态信息会显示在界面底部的状态框中，出现错误时请查看详细信息。

## 开发信息

- **开发语言**: Python
- **GUI框架**: PySide6
- **音频处理**: pydub
- **数值计算**: numpy

## 许可证

本项目仅供学习和个人使用。
