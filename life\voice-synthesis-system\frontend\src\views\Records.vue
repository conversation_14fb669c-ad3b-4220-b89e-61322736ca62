<template>
  <div class="records-container">
    <el-card>
      <div slot="header">
        <span>合成记录</span>
        <div style="float: right;">
          <el-button @click="loadRecords">刷新</el-button>
        </div>
      </div>

      <el-table :data="records" style="width: 100%" v-loading="loading">
        <el-table-column prop="text" label="合成文本" width="300" show-overflow-tooltip></el-table-column>
        <el-table-column prop="modelName" label="模型" width="120"></el-table-column>
        <el-table-column prop="pointsCost" label="消耗积分" width="100"></el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : scope.row.status === 0 ? 'danger' : 'warning'">
              {{ scope.row.status === 1 ? '成功' : scope.row.status === 0 ? '失败' : '处理中' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fileSize" label="文件大小" width="100">
          <template slot-scope="scope">
            {{ scope.row.fileSize ? formatFileSize(scope.row.fileSize) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180"></el-table-column>
        <el-table-column label="操作" width="150">
          <template slot-scope="scope">
            <el-button 
              v-if="scope.row.audioUrl && scope.row.status === 1" 
              type="text" 
              @click="playAudio(scope.row.audioUrl)">
              播放
            </el-button>
            <el-button 
              v-if="scope.row.audioUrl && scope.row.status === 1" 
              type="text" 
              @click="downloadAudio(scope.row.audioUrl)">
              下载
            </el-button>
            <span v-if="scope.row.status === 0" style="color: #F56C6C;">
              {{ scope.row.errorMessage || '合成失败' }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </el-card>

    <!-- 音频播放对话框 -->
    <el-dialog title="音频播放" :visible.sync="audioDialogVisible" width="500px">
      <div class="audio-player">
        <audio controls :src="currentAudioUrl" style="width: 100%"></audio>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Records',
  data() {
    return {
      records: [],
      loading: false,
      currentPage: 1,
      pageSize: 20,
      total: 0,
      audioDialogVisible: false,
      currentAudioUrl: ''
    }
  },
  methods: {
    async loadRecords() {
      this.loading = true
      try {
        // const response = await this.$http.get('/user/synthesis-records', {
        //   params: {
        //     page: this.currentPage,
        //     size: this.pageSize
        //   }
        // })
        // this.records = response.data.records
        // this.total = response.data.total
        
        // 模拟数据
        this.records = [
          {
            id: 1,
            text: '这是一个测试文本，用于语音合成',
            modelName: 'zhexue_lao',
            pointsCost: 10,
            status: 1,
            fileSize: 1024000,
            audioUrl: '/audio/test1.wav',
            createTime: '2023-12-01 10:30:00'
          },
          {
            id: 2,
            text: '另一个语音合成测试',
            modelName: 'xiaoshuo_nan',
            pointsCost: 10,
            status: 1,
            fileSize: 2048000,
            audioUrl: '/audio/test2.wav',
            createTime: '2023-12-01 09:15:00'
          },
          {
            id: 3,
            text: '失败的合成尝试',
            modelName: 'zhexue_lao',
            pointsCost: 10,
            status: 0,
            errorMessage: '模型加载失败',
            createTime: '2023-11-30 16:45:00'
          }
        ]
        this.total = 3
      } catch (error) {
        console.error('加载合成记录失败:', error)
        this.$message.error('加载合成记录失败')
      } finally {
        this.loading = false
      }
    },

    handleSizeChange(val) {
      this.pageSize = val
      this.loadRecords()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.loadRecords()
    },

    playAudio(audioUrl) {
      this.currentAudioUrl = `http://localhost:8080/api${audioUrl}`
      this.audioDialogVisible = true
    },

    downloadAudio(audioUrl) {
      const link = document.createElement('a')
      link.href = `http://localhost:8080/api${audioUrl}`
      link.download = `synthesis_${Date.now()}.wav`
      link.click()
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  },
  mounted() {
    this.loadRecords()
  }
}
</script>

<style scoped>
.records-container {
  padding: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.audio-player {
  text-align: center;
  padding: 20px;
}
</style>
