<template>
  <div class="profile-container">
    <el-row :gutter="20">
      <!-- 用户信息 -->
      <el-col :span="12">
        <el-card>
          <div slot="header">
            <span>个人信息</span>
            <el-button style="float: right;" type="text" @click="editMode = !editMode">
              {{ editMode ? '取消编辑' : '编辑信息' }}
            </el-button>
          </div>
          
          <el-form :model="userForm" :rules="rules" ref="userForm" label-width="80px">
            <el-form-item label="用户名">
              <el-input v-model="userForm.username" disabled></el-input>
            </el-form-item>
            <el-form-item label="昵称" prop="nickname">
              <el-input v-model="userForm.nickname" :disabled="!editMode"></el-input>
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="userForm.email" :disabled="!editMode"></el-input>
            </el-form-item>
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="userForm.phone" :disabled="!editMode"></el-input>
            </el-form-item>
            <el-form-item label="注册时间">
              <el-input v-model="userForm.createTime" disabled></el-input>
            </el-form-item>
            <el-form-item v-if="editMode">
              <el-button type="primary" @click="saveProfile" :loading="saving">保存</el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 积分信息 -->
      <el-col :span="12">
        <el-card>
          <div slot="header">
            <span>积分信息</span>
          </div>
          
          <div class="points-info">
            <div class="points-display">
              <i class="el-icon-coin"></i>
              <span class="points-number">{{ currentUser.points }}</span>
              <span class="points-label">当前积分</span>
            </div>
            
            <div class="points-stats">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="stat-item">
                    <h4>可用次数</h4>
                    <p>{{ Math.floor(currentUser.points / 10) }}</p>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="stat-item">
                    <h4>合成消耗</h4>
                    <p>10积分/次</p>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-card>

        <!-- 积分记录 -->
        <el-card style="margin-top: 20px;">
          <div slot="header">
            <span>积分记录</span>
          </div>
          
          <el-table :data="pointRecords" style="width: 100%" max-height="300">
            <el-table-column prop="type" label="类型" width="80">
              <template slot-scope="scope">
                <el-tag :type="scope.row.type === 1 ? 'success' : 'warning'" size="mini">
                  {{ scope.row.type === 1 ? '获得' : '消耗' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="points" label="积分" width="80">
              <template slot-scope="scope">
                <span :style="{ color: scope.row.type === 1 ? '#67C23A' : '#E6A23C' }">
                  {{ scope.row.type === 1 ? '+' : '-' }}{{ scope.row.points }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="说明" show-overflow-tooltip></el-table-column>
            <el-table-column prop="createTime" label="时间" width="150"></el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 修改密码对话框 -->
    <el-dialog title="修改密码" :visible.sync="passwordDialogVisible" width="400px">
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordForm" label-width="100px">
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input type="password" v-model="passwordForm.oldPassword"></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input type="password" v-model="passwordForm.newPassword"></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input type="password" v-model="passwordForm.confirmPassword"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="passwordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="changePassword">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Profile',
  data() {
    const validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }

    return {
      editMode: false,
      saving: false,
      userForm: {
        username: '',
        nickname: '',
        email: '',
        phone: '',
        createTime: ''
      },
      rules: {
        nickname: [
          { required: true, message: '请输入昵称', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      pointRecords: [],
      passwordDialogVisible: false,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入当前密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { validator: validatePass2, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['currentUser'])
  },
  methods: {
    loadUserInfo() {
      if (this.currentUser) {
        this.userForm = {
          username: this.currentUser.username,
          nickname: this.currentUser.nickname,
          email: this.currentUser.email,
          phone: this.currentUser.phone,
          createTime: this.currentUser.createTime
        }
      }
    },

    async saveProfile() {
      this.$refs.userForm.validate(async (valid) => {
        if (valid) {
          this.saving = true
          try {
            // await this.$http.put('/user/profile', this.userForm)
            this.$message.success('个人信息更新成功')
            this.editMode = false
            // 更新store中的用户信息
            // this.$store.commit('SET_USER', updatedUser)
          } catch (error) {
            console.error('更新个人信息失败:', error)
          } finally {
            this.saving = false
          }
        }
      })
    },

    resetForm() {
      this.loadUserInfo()
    },

    async loadPointRecords() {
      try {
        // const response = await this.$http.get('/user/point-records')
        // this.pointRecords = response.data.records
        
        // 模拟数据
        this.pointRecords = [
          {
            type: 1,
            points: 100,
            description: '注册赠送积分',
            createTime: '2023-12-01 10:00:00'
          },
          {
            type: 2,
            points: 10,
            description: '语音合成消耗积分',
            createTime: '2023-12-01 10:30:00'
          },
          {
            type: 2,
            points: 10,
            description: '语音合成消耗积分',
            createTime: '2023-12-01 11:00:00'
          }
        ]
      } catch (error) {
        console.error('加载积分记录失败:', error)
      }
    },

    async changePassword() {
      this.$refs.passwordForm.validate(async (valid) => {
        if (valid) {
          try {
            // await this.$http.put('/user/password', this.passwordForm)
            this.$message.success('密码修改成功')
            this.passwordDialogVisible = false
            this.passwordForm = {
              oldPassword: '',
              newPassword: '',
              confirmPassword: ''
            }
          } catch (error) {
            console.error('修改密码失败:', error)
          }
        }
      })
    }
  },
  mounted() {
    this.loadUserInfo()
    this.loadPointRecords()
  }
}
</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.points-info {
  text-align: center;
}

.points-display {
  margin-bottom: 30px;
}

.points-display i {
  font-size: 48px;
  color: #409EFF;
  display: block;
  margin-bottom: 10px;
}

.points-number {
  font-size: 36px;
  font-weight: bold;
  color: #409EFF;
  display: block;
}

.points-label {
  color: #666;
  font-size: 14px;
}

.points-stats {
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.stat-item {
  text-align: center;
}

.stat-item h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 14px;
}

.stat-item p {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
}
</style>
