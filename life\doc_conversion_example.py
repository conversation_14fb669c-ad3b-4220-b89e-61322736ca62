#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DOC转换功能示例
演示如何使用文件转换器进行DOC和DOCX之间的转换
"""

import os
from pathlib import Path
from docx import Document

def create_sample_docx():
    """创建一个示例DOCX文件用于测试"""
    doc = Document()
    
    # 添加标题
    doc.add_heading('文档转换测试', 0)
    
    # 添加段落
    doc.add_paragraph('这是一个用于测试DOC/DOCX转换功能的示例文档。')
    
    # 添加子标题
    doc.add_heading('功能特性', level=1)
    
    # 添加列表
    doc.add_paragraph('支持的转换类型：', style='List Bullet')
    doc.add_paragraph('DOC → DOCX', style='List Bullet')
    doc.add_paragraph('DOCX → DOC', style='List Bullet')
    doc.add_paragraph('保持文本内容完整', style='List Bullet')
    
    # 添加另一个段落
    doc.add_paragraph('转换过程会尽可能保持原文档的文本内容，但某些格式可能会丢失。')
    
    # 保存文件
    sample_file = Path("sample_document.docx")
    doc.save(sample_file)
    print(f"✓ 创建示例DOCX文件: {sample_file.absolute()}")
    return sample_file

def test_doc_conversion():
    """测试DOC转换功能"""
    from file_converter_gui import FileConverter
    
    converter = FileConverter()
    
    # 创建示例DOCX文件
    docx_file = create_sample_docx()
    
    # 测试DOCX到DOC的转换
    doc_file = Path("converted_document.doc")
    print(f"\n开始转换: {docx_file} → {doc_file}")
    
    success = converter.convert_file(
        str(docx_file), 
        str(doc_file), 
        '.docx', 
        '.doc'
    )
    
    if success:
        print(f"✓ 转换成功! 输出文件: {doc_file.absolute()}")
        print(f"文件大小: {doc_file.stat().st_size} 字节")
    else:
        print("✗ 转换失败")
    
    # 如果转换成功，尝试反向转换
    if success and doc_file.exists():
        print(f"\n开始反向转换: {doc_file} → converted_back.docx")
        
        docx_back_file = Path("converted_back.docx")
        reverse_success = converter.convert_file(
            str(doc_file),
            str(docx_back_file),
            '.doc',
            '.docx'
        )
        
        if reverse_success:
            print(f"✓ 反向转换成功! 输出文件: {docx_back_file.absolute()}")
        else:
            print("✗ 反向转换失败")

def show_conversion_methods():
    """显示可用的转换方法"""
    print("DOC转换支持的方法:")
    print("1. LibreOffice命令行工具 (推荐)")
    print("   - 提供最高质量的转换")
    print("   - 保持格式和样式")
    print("   - 需要安装LibreOffice")
    
    print("\n2. 文本提取方式 (备用)")
    print("   - 提取文本内容创建新文档")
    print("   - 可能丢失格式信息")
    print("   - 不需要额外软件")
    
    print("\n安装LibreOffice的建议:")
    print("- Windows: 从 https://www.libreoffice.org/ 下载安装")
    print("- macOS: brew install --cask libreoffice")
    print("- Linux: sudo apt-get install libreoffice")

def main():
    """主函数"""
    print("DOC转换功能演示")
    print("=" * 40)
    
    try:
        # 显示转换方法信息
        show_conversion_methods()
        
        print("\n" + "=" * 40)
        
        # 测试转换功能
        test_doc_conversion()
        
        print("\n转换测试完成!")
        print("\n要使用GUI界面进行批量转换，请运行:")
        print("python file_converter_gui.py")
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装所需依赖:")
        print("pip install -r requirements.txt")
    except Exception as e:
        print(f"运行过程中出现错误: {e}")

if __name__ == "__main__":
    main()
