#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试DOC转换功能
"""

def test_doc_support():
    """测试DOC转换支持"""
    try:
        from file_converter_gui import FileConverter
        
        converter = FileConverter()
        
        # 检查支持的格式
        print("支持的文档格式:")
        doc_info = converter.supported_conversions['document']
        print(f"输入格式: {doc_info['extensions']}")
        print(f"输出格式: {doc_info['target_formats']}")
        
        # 检查DOC格式是否在支持列表中
        if '.doc' in doc_info['extensions']:
            print("✓ DOC格式已添加到输入格式列表")
        else:
            print("✗ DOC格式未在输入格式列表中")
            
        if '.doc' in doc_info['target_formats']:
            print("✓ DOC格式已添加到输出格式列表")
        else:
            print("✗ DOC格式未在输出格式列表中")
        
        # 测试格式识别
        doc_category = converter.get_file_category('.doc')
        print(f"DOC文件类别识别: {doc_category}")
        
        # 测试目标格式获取
        target_formats = converter.get_target_formats('.doc')
        print(f"DOC可转换的目标格式: {target_formats}")
        
        docx_target_formats = converter.get_target_formats('.docx')
        print(f"DOCX可转换的目标格式: {docx_target_formats}")
        
        print("\n✓ DOC转换功能已成功集成到文件转换器中!")
        
    except ImportError as e:
        print(f"导入错误: {e}")
    except Exception as e:
        print(f"测试错误: {e}")

def show_usage_info():
    """显示使用信息"""
    print("\n使用说明:")
    print("1. 启动GUI应用: python file_converter_gui.py")
    print("2. 选择包含DOC文件的源文件夹")
    print("3. 选择输出文件夹")
    print("4. 在格式选择中选择 '.doc' 作为源格式")
    print("5. 选择目标格式（如 '.docx'）")
    print("6. 点击'开始转换'")
    
    print("\n转换质量说明:")
    print("- 如果系统安装了LibreOffice，将获得高质量转换")
    print("- 否则使用文本提取方式，可能丢失格式信息")
    print("- 建议安装LibreOffice以获得最佳效果")

if __name__ == "__main__":
    print("DOC转换功能快速测试")
    print("=" * 30)
    test_doc_support()
    show_usage_info()
